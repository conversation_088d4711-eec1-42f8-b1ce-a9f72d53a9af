import os
os.environ['CUDA_VISIBLE_DEVICES'] = '0'

# definations

import argparse
import datetime
import pathlib
import sys
import time
import cv2
import lietorch
import torch
import tqdm
import yaml
import pickle
import os
import json
import numpy as np
from logger import logger
from mast3r_slam.global_opt import FactorGraph

from mast3r_slam.config import load_config, config, set_global_config
from mast3r_slam.dataloader import Intrinsics, load_dataset
import mast3r_slam.evaluate as eval
from mast3r_slam.frame import Mode, create_frame, Frame
from mast3r_slam.retrieval_database import RetrievalDatabase
from mast3r_slam.mast3r_utils import (
    load_mast3r,
    load_retriever,
    mast3r_inference_mono,
)
from mast3r_slam.tracker import FrameTracker

class SimpleKeyframes:
    """简化版的关键帧管理器，不使用multiprocessing"""
    def __init__(self, h=None, w=None, device='cuda'):
        self.h = h
        self.w = w
        self.device = device
        
        # 关键帧列表
        self.keyframes = []
        
        # 相机位姿 
        self.T_WC = []
        
        # 相机内参
        self.K = None

    def from_pickle(self, path, device):
        with open(path, 'rb') as f:
            data = pickle.load(f)
        for frame_data in data:
            if self.h is None or self.w is None:
                self.h, self.w = frame_data['img_shape'][0].tolist()
            frame = Frame(
                frame_id=frame_data['frame_id'],
                img=torch.from_numpy(frame_data['img']).to(device),
                img_shape=torch.from_numpy(frame_data['img_shape']).to(device),
                img_true_shape=torch.from_numpy(frame_data['img_true_shape']).to(device),
                uimg=torch.from_numpy(frame_data['uimg']).to(device),
                T_WC=lietorch.Sim3(torch.from_numpy(frame_data['T_WC']).to(device)),
                X_canon=torch.from_numpy(frame_data['X_canon']).to(device) if frame_data['X_canon'] is not None else None,
                C=torch.from_numpy(frame_data['C']).to(device) if frame_data['C'] is not None else None,
                feat=torch.from_numpy(frame_data['feat']).to(device) if frame_data['feat'] is not None else None,
                pos=torch.from_numpy(frame_data['pos']).to(device) if frame_data['pos'] is not None else None,
                N=frame_data['N'],
                N_updates=frame_data['N_updates'],
                K=torch.from_numpy(frame_data['K']).to(device) if frame_data['K'] is not None else None,
            )
            self.append(frame)
        return self
        
    def __len__(self):
        return len(self.keyframes)
    
    def __getitem__(self, idx):
        return self.keyframes[idx]
    
    def __setitem__(self, idx, value):
        self.keyframes[idx] = value
        # Update the stored T_WC data as well
        self.T_WC[idx] = value.T_WC.data.clone()
    
    def append(self, frame):
        self.keyframes.append(frame)
        # Store the Sim3 data, not the object itself
        self.T_WC.append(frame.T_WC.data.clone())
    
    def pop_last(self):
        if self.keyframes:
            self.keyframes.pop()
            self.T_WC.pop()
    
    def set_intrinsics(self, K):
        self.K = K
        
    @property
    def n(self):
        return len(self.keyframes)
    
    def num_frames(self):
        return len(self.keyframes)
    
    def last_keyframe(self):
        if len(self.keyframes) == 0:
            return None
        return self.keyframes[-1]
    
    def update_T_WCs(self, T_WCs, idx):
        """更新指定索引的相机位姿"""
        for i, frame_idx in enumerate(idx):
            self.keyframes[frame_idx].T_WC = lietorch.Sim3(T_WCs[i].data)
            self.T_WC[frame_idx] = T_WCs[i].data.clone()

class SimpleStates:
    """简化版的状态管理器"""
    def __init__(self, keyframes=None, retrieval_database=None, factor_graph=None, tracker=None):
        self._mode = Mode.INIT
        self._frame = None
        self.global_optimizer_tasks = []
        self.edges_ii = []
        self.edges_jj = []
        self.keyframes = keyframes
        self.retrieval_database = retrieval_database
        self.factor_graph = factor_graph
        self.tracker = tracker

    def merge(self, other):
        new_keyframe_ids = []
        offset = len(self.keyframes)
        for i, frame in enumerate(other.keyframes):
            kf_i = len(self.keyframes)
            new_keyframe_ids.append(kf_i)
            self.keyframes.append(frame)
            assert len(self.keyframes) == len(self.factor_graph.frames)
            retrieval_inds = self.retrieval_database.update(
                frame, 
                add_after_query=False,   # 暂时不添加到检索数据库中，建立因子图后再添加
                k=config["retrieval"]["k"],
                min_thresh=config["retrieval"]["min_thresh"],
            )
            if len(retrieval_inds) == 0 and i == 0:
                retrieval_inds = [kf_i-1]
                logger.warning(f"Adding {kf_i-1} to {kf_i} as first match")
            if len(retrieval_inds) > 0:
                # logger.info(f"Adding {len(retrieval_inds)} edges from kf {kf_i} to {retrieval_inds}")
                self.factor_graph.add_factors([kf_i]*len(retrieval_inds), retrieval_inds, config["local_opt"]["min_match_frac"], is_reloc=False)

        self.factor_graph.vertices.extend((np.array(other.factor_graph.vertices)+offset).tolist())
        self.factor_graph.edges.extend((np.array(other.factor_graph.edges)+offset).tolist())
        self.factor_graph.ii = torch.cat([self.factor_graph.ii, other.factor_graph.ii+offset], dim=0)
        self.factor_graph.jj = torch.cat([self.factor_graph.jj, other.factor_graph.jj+offset], dim=0)
        self.factor_graph.idx_ii2jj = torch.cat([self.factor_graph.idx_ii2jj, other.factor_graph.idx_ii2jj], dim=0)
        self.factor_graph.idx_jj2ii = torch.cat([self.factor_graph.idx_jj2ii, other.factor_graph.idx_jj2ii], dim=0)
        self.factor_graph.valid_match_j = torch.cat([self.factor_graph.valid_match_j, other.factor_graph.valid_match_j], dim=0)
        self.factor_graph.valid_match_i = torch.cat([self.factor_graph.valid_match_i, other.factor_graph.valid_match_i], dim=0)
        self.factor_graph.Q_ii2jj = torch.cat([self.factor_graph.Q_ii2jj, other.factor_graph.Q_ii2jj], dim=0)
        self.factor_graph.Q_jj2ii = torch.cat([self.factor_graph.Q_jj2ii, other.factor_graph.Q_jj2ii], dim=0)

        for frame in other.keyframes:
            self.retrieval_database.update(
                frame,
                add_after_query=True,
                k=config["retrieval"]["k"],
                min_thresh=config["retrieval"]["min_thresh"],
            )

    def save(self, savedir, basename, intrinsics=None):
        logger.info(f"Saving results to {savedir} {basename}")
        eval.save_keyframes(f"{savedir}/keyframes", None, self.keyframes)
        eval.save_traj(savedir, f"{basename}.txt", None, self.keyframes, intrinsics=intrinsics)
        eval.save_to_pkl(savedir, f"{basename}.pkl", self.keyframes, c_conf_threshold=0.5)
        eval.save_factor_graph(savedir, f"{basename}.json", self.factor_graph)
        eval.save_reconstruction(savedir, f"{basename}.ply", self.keyframes, c_conf_threshold=0.5)

    def load(self, savedir, basename, model, device, intrinsics=None):
        self.set_mode(Mode.TRACKING)
        self.keyframes = SimpleKeyframes().from_pickle(f"{savedir}/{basename}.pkl", device)
        logger.info(f"Loaded {len(self.keyframes)} keyframes from {savedir}/{basename}.pkl")
        self.tracker = FrameTracker(model, self.keyframes, device)
        logger.info(f"Loaded tracker")
        with open(os.path.join(savedir, f"{basename}.json"), 'r') as f:
            factor_graph_data = json.load(f)
        self.factor_graph = FactorGraph(model, self.keyframes, K=intrinsics, device=device)
        if len(factor_graph_data['edges']) > 0:
            ii = [e[0] for e in factor_graph_data['edges']]
            jj = [e[1] for e in factor_graph_data['edges']]
            assert len(ii) == len(jj)
            n = 0
            # self.factor_graph.add_factors(ii, jj, config["local_opt"]["min_match_frac"])
            bs = 10
            for i in range(0, len(ii), bs):
                ii_i = ii[i:i+bs]
                jj_i = jj[i:i+bs]
                self.factor_graph.add_factors(ii_i, jj_i, config["local_opt"]["min_match_frac"])
                n += len(ii_i)
            logger.info(f"Loaded factor graph with {len(factor_graph_data['edges'])} edges   vs {n}")
        self.retrieval_database = load_retriever(model, device=device)
        for frame in self.keyframes:
            self.retrieval_database.update(
                frame,
                add_after_query=True,
                k=config["retrieval"]["k"],
                min_thresh=config["retrieval"]["min_thresh"],
            )
        logger.info(f"Loaded retrieval database")
        return self.keyframes, self.factor_graph, self.retrieval_database, self.tracker
        
    def get_mode(self):
        return self._mode
    
    def set_mode(self, mode):
        self._mode = mode
        logger.info(f"Switching to mode {mode}")
    
    def get_frame(self):
        return self._frame
    
    def set_frame(self, frame):
        self._frame = frame
    
    def queue_global_optimization(self, idx):
        self.global_optimizer_tasks.append(idx)
        logger.info(f"Queueing global optimization for frame {idx}: {self.global_optimizer_tasks}")

class States:
    """状态管理器"""
    def __init__(self):
        self.tracks = []

    @property
    def current_track(self):
        return self.tracks[-1]
    
    def get_mode(self):
        return self.current_track.get_mode()

    def set_mode(self, mode):
        self.current_track.set_mode(mode)

    def get_frame(self):
        return self.current_track.get_frame()

    def set_frame(self, frame):
        self.current_track.set_frame(frame)

    def queue_global_optimization(self, idx):
        self.current_track.queue_global_optimization(idx)

    @property
    def keyframes(self):
        return self.current_track.keyframes

    @property
    def retrieval_database(self):
        return self.current_track.retrieval_database

    @property
    def factor_graph(self):
        return self.current_track.factor_graph

    @property
    def tracker(self):
        return self.current_track.tracker
    
    @property
    def global_optimizer_tasks(self):
        return self.current_track.global_optimizer_tasks
    
    def new_track(self, keyframes, retrieval_database, factor_graph, tracker):
        states = SimpleStates(keyframes, retrieval_database, factor_graph, tracker)
        self.tracks.append(states)

    def set_keyframes(self, keyframes):
        self.current_track.keyframes = keyframes

    def set_retrieval_database(self, retrieval_database):
        self.current_track.retrieval_database = retrieval_database

    def set_factor_graph(self, factor_graph):
        self.current_track.factor_graph = factor_graph

    def set_tracker(self, tracker):
        self.current_track.tracker = tracker

    def show(self, voxel_size=0.1):
        return self.current_track.show(voxel_size)

    def save(self, savedir, basename, intrinsics=None):
        tracks_info = {
            'savedir': str(savedir),
            'intrinsics': intrinsics,
            'tracks': []
        }
        for i, track in enumerate(self.tracks):
            track_name = f"{basename}_{i}"
            tracks_info['tracks'].append(track_name)
            track.save(savedir, track_name, intrinsics=intrinsics)
        with open(os.path.join(savedir, f"{basename}_tracks.json"), 'w') as f:
            json.dump(tracks_info, f, ensure_ascii=False)

    def load(self, savedir, basename, model, device):
        logger.info(f"Loading track from {savedir} {basename}")
        keyframes = eval.load_keyframes_tracks(savedir, basename)
        pass

def relocalization(frame, keyframes: SimpleKeyframes, factor_graph: FactorGraph, retrieval_database: FrameTracker):
    # we are adding and then removing from the keyframe, so we need to be careful.
    kf_idx = []
    retrieval_inds = retrieval_database.update(
        frame,
        add_after_query=False,
        k=config["retrieval"]["k"],
        min_thresh=config["retrieval"]["min_thresh"],
    )
    logger.info(f"First RETRIEVAL: {retrieval_inds}")
    kf_idx += retrieval_inds
    successful_loop_closure = False
    if kf_idx:
        keyframes.append(frame)
        n_kf = len(keyframes)
        kf_idx = list(kf_idx)  # convert to list
        frame_idx = [n_kf - 1] * len(kf_idx)
        logger.info(f"kf_idx frame_idx is: {kf_idx}, {frame_idx}")
        logger.info(f"RELOCALIZING against kf {n_kf - 1} and {kf_idx}")
        if factor_graph.add_factors(
            frame_idx,
            kf_idx,
            config["reloc"]["min_match_frac"],
            is_reloc=config["reloc"]["strict"],
        ):
            retrieval_database.update(
                frame,
                add_after_query=True,
                k=config["retrieval"]["k"],
                min_thresh=config["retrieval"]["min_thresh"],
            )
            logger.info("Success! Relocalized")
            successful_loop_closure = True
            # Update the frame's T_WC and the stored T_WC data
            keyframes.keyframes[n_kf - 1].T_WC = lietorch.Sim3(keyframes.T_WC[kf_idx[0]])
            keyframes.T_WC[n_kf - 1] = keyframes.T_WC[kf_idx[0]].clone()
        else:
            keyframes.pop_last()
            logger.info("Failed to relocalize")

    if successful_loop_closure:
        if config["use_calib"]:
            factor_graph.solve_GN_calib()
        else:
            factor_graph.solve_GN_rays()
    return successful_loop_closure

def process_keyframe_optimization(idx, keyframes: SimpleKeyframes, factor_graph: FactorGraph, retrieval_database: RetrievalDatabase, states: SimpleStates):
    """处理关键帧优化任务"""
    logger.info(f"Processing keyframe optimization for frame {idx}")
    # Graph Construction
    kf_idx = []
    # k to previous consecutive keyframes
    n_consec = 1
    for j in range(min(n_consec, idx)):
        kf_idx.append(idx - 1 - j)
    frame = keyframes[idx]
    retrieval_inds = retrieval_database.update(
        frame,
        add_after_query=True,
        k=config["retrieval"]["k"],
        min_thresh=config["retrieval"]["min_thresh"],
    )
    # if len(retrieval_inds) == 0 and idx != 0:
    #     retrieval_inds = [idx - 1]      # HARDCODE: 没有检索结果时，强行添加前一个关键帧
    logger.info(f"Database retrieval {idx}: {retrieval_inds}")
    kf_idx += retrieval_inds

    lc_inds = set(retrieval_inds)
    lc_inds.discard(idx - 1)
    if len(lc_inds) > 0:
        logger.info("Database retrieval", idx, ": ", lc_inds)

    kf_idx = set(kf_idx)  # Remove duplicates by using set
    kf_idx.discard(idx)  # Remove current kf idx if included
    kf_idx = list(kf_idx)  # convert to list
    frame_idx = [idx] * len(kf_idx)
    if kf_idx:
        factor_graph.add_factors(
            kf_idx, frame_idx, config["local_opt"]["min_match_frac"]
        )

    states.edges_ii = factor_graph.ii.cpu().tolist()
    states.edges_jj = factor_graph.jj.cpu().tolist()

    if config["use_calib"]:
        factor_graph.solve_GN_calib()
    else:
        factor_graph.solve_GN_rays()

def init(args, model, device):
    dataset = load_dataset(args.dataset)
    dataset.subsample(config["dataset"]["subsample"])
    h, w = dataset.get_img_shape()[0]

    if args.calib:
        with open(args.calib, "r") as f:
            intrinsics = yaml.load(f, Loader=yaml.SafeLoader)
        config["use_calib"] = True
        dataset.use_calibration = True
        dataset.camera_intrinsics = Intrinsics.from_calib(
                dataset.img_size,
                intrinsics["width"],
                intrinsics["height"],
                intrinsics["calibration"],
            )

    keyframes = SimpleKeyframes(h, w, device)
    states = States()

    has_calib = dataset.has_calib()
    use_calib = config["use_calib"]

    if use_calib and not has_calib:
        logger.error("No calibration provided for this dataset!")
        sys.exit(0)
    K = None
    if use_calib:
        K = torch.from_numpy(dataset.camera_intrinsics.K_frame).to(
                device, dtype=torch.float32
            )
        keyframes.set_intrinsics(K)

    # remove the trajectory from the previous run
    if dataset.save_results:
        save_dir, seq_name = eval.prepare_savedir(args.save_as, dataset)
        traj_file = save_dir / f"{seq_name}.txt"
        recon_file = save_dir / f"{seq_name}.ply"
        if traj_file.exists():
            traj_file.unlink()
        if recon_file.exists():
            recon_file.unlink()

    tracker = FrameTracker(model, keyframes, device)
        
    # 初始化后端组件
    factor_graph = FactorGraph(model, keyframes, K, device)
    retrieval_database = load_retriever(model, device=device)

    states.new_track(keyframes, retrieval_database, factor_graph, tracker)

    i = 0
    return i, dataset, states, K, save_dir, seq_name


def show(keyframes, factor_graph, show_cloudpoints=True, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.1, highlight_nodes=[], use_calib=False):
    import open3d as o3d
    import plotly.graph_objects as go
    from mast3r_slam.geometry import constrain_points_to_ray

    if len(keyframes) == 0:
        return

    # 创建plotly图形对象
    fig = go.Figure()

    # 只有在show_cloudpoints为True时才处理和显示点云
    if show_cloudpoints:
        # 在同一个图中显示点云和连接图
        pointclouds = []
        colors = []
        for frame in keyframes:
            if use_calib:
                X_canon = constrain_points_to_ray(frame.img_shape.flatten()[:2], frame.X_canon[None], frame.K)
                frame.X_canon = X_canon.squeeze(0)
            pW = frame.T_WC.act(frame.X_canon).cpu().numpy().reshape(-1, 3)
            color = (frame.uimg.cpu().numpy() * 255).astype(np.uint8).reshape(-1, 3)
            valid = (frame.get_average_conf().cpu().numpy().astype(np.float32).reshape(-1) > 0.5)
            # 增加更严格的置信度过滤
            valid = valid & (frame.get_average_conf().cpu().numpy().astype(np.float32).reshape(-1) > 0.7)
            # 随机采样，只保留一部分点
            if np.sum(valid) > 10000:
                indices = np.where(valid)[0]
                sample_size = min(10000, len(indices))
                sample_indices = np.random.choice(indices, sample_size, replace=False)
                new_valid = np.zeros_like(valid)
                new_valid[sample_indices] = True
                valid = new_valid
            pointclouds.append(pW[valid])
            colors.append(color[valid])
        pointclouds = np.concatenate(pointclouds, axis=0)
        colors = np.concatenate(colors, axis=0)
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(pointclouds)
        pcd.colors = o3d.utility.Vector3dVector(colors)
        pcd = pcd.voxel_down_sample(voxel_size=voxel_size)
        points = np.asarray(pcd.points)
        colors = np.asarray(pcd.colors) if pcd.has_colors() else None
        colors = colors.astype(np.uint8)

        # 创建plotly 3D散点图
        # 根据show_colors参数决定是否使用颜色
        if show_colors and colors is not None:
            colors_rgb = [f'rgb({r},{g},{b})' for r, g, b in colors]
            marker_dict = dict(size=1.5, color=colors_rgb)
        else:
            marker_dict = dict(size=1.5, color='lightgray')

        pointcloud_trace = go.Scatter3d(
            x=points[:, 0],
            y=points[:, 1],
            z=points[:, 2],
            mode='markers',
            marker=marker_dict,
            showlegend=False
        )
        fig.add_trace(pointcloud_trace)

    if show_poses:
        nodes = {}
        camera_poses = {}
        for i, frame in zip(factor_graph.vertices, factor_graph.frames):
            # Extract position
            pose_data = frame.T_WC.data.cpu().numpy()[0]
            x, y, z = pose_data[:3].tolist()
            nodes[i] = (x, y, z)

            # Extract rotation matrix from Sim3 transformation
            # Sim3 format: [tx, ty, tz, qx, qy, qz, qw, s] where q is quaternion and s is scale
            qx, qy, qz, qw = pose_data[3:7]

            # Convert quaternion to rotation matrix
            R = np.array([
                [1 - 2 * (qy**2 + qz**2), 2 * (qx*qy - qw*qz), 2 * (qx*qz + qw*qy)],
                [2 * (qx*qy + qw*qz), 1 - 2 * (qx**2 + qz**2), 2 * (qy*qz - qw*qx)],
                [2 * (qx*qz - qw*qy), 2 * (qy*qz + qw*qx), 1 - 2 * (qx**2 + qy**2)]
            ])

            camera_poses[i] = {'position': np.array([x, y, z]), 'rotation': R}

        edges = factor_graph.edges

        highlight_nodes = set(highlight_nodes)
        if len(highlight_nodes) == 0:
            if nodes:
                highlight_nodes.add(max(nodes.keys()))
        highlight_connected_nodes = set()
        for edge in edges:
            if edge[0] in highlight_nodes:
                highlight_connected_nodes.add(edge[1])
            elif edge[1] in highlight_nodes:
                highlight_connected_nodes.add(edge[0])

        # Create edge traces
        edge_traces = []
        for edge in edges:
            start_node = nodes[edge[0]]
            end_node = nodes[edge[1]]

            # 如果连线涉及最后一个节点，使用绿色；否则使用蓝色
            if edge[0] in highlight_nodes or edge[1] in highlight_nodes:
                line_color = 'red'
                line_width = 5  # 稍微加粗绿色线条
            else:
                line_color = 'blue'
                line_width = 4

            edge_trace = go.Scatter3d(
                x=[start_node[0], end_node[0], None],
                y=[start_node[1], end_node[1], None],
                z=[start_node[2], end_node[2], None],
                mode='lines',
                line=dict(width=line_width, color=line_color),
                hoverinfo='none',
                showlegend=False
            )
            edge_traces.append(edge_trace)

        # Create camera pose arrows instead of simple nodes
        arrow_traces = []
        arrow_length = 0.4  # Adjust this to change arrow size

        for i, pose_info in camera_poses.items():
            pos = pose_info['position']
            R = pose_info['rotation']

            # Camera coordinate system: typically Z-axis points forward (camera direction)
            # X-axis points right, Y-axis points down
            forward_dir = R[:, 2]  # Z-axis (camera forward direction)
            right_dir = R[:, 0]    # X-axis (camera right direction)
            up_dir = -R[:, 1]      # -Y-axis (camera up direction, negative because Y typically points down)

            # 确定箭头颜色：最后一个节点用红色，与最后节点相连的用橙色，其他用蓝色
            if i in highlight_nodes:
                arrow_color = 'red'
                marker_color = 'darkred'
            elif i in highlight_connected_nodes:
                arrow_color = 'orange'
                marker_color = 'darkorange'
            else:
                arrow_color = 'gray'
                marker_color = 'darkgray'

            if show_arrows:
                # Create arrow shaft (forward direction)
                arrow_end = pos + forward_dir * arrow_length
                arrow_trace = go.Scatter3d(
                    x=[pos[0], arrow_end[0], None],
                    y=[pos[1], arrow_end[1], None],
                    z=[pos[2], arrow_end[2], None],
                    mode='lines',
                    line=dict(width=1, color=arrow_color),
                    name=f'Camera {i}',
                    hoverinfo='name',
                    showlegend=False
                )
                arrow_traces.append(arrow_trace)

                # Create arrow head (cone-like structure)
                head_length = arrow_length * 0.3
                head_width = arrow_length * 0.1

                # Create arrow head using two lines forming a cone
                head_base = arrow_end - forward_dir * head_length
                head_point1 = head_base + right_dir * head_width + up_dir * head_width
                head_point2 = head_base - right_dir * head_width + up_dir * head_width
                head_point3 = head_base + right_dir * head_width - up_dir * head_width
                head_point4 = head_base - right_dir * head_width - up_dir * head_width

                # Arrow head lines
                for head_point in [head_point1, head_point2, head_point3, head_point4]:
                    head_trace = go.Scatter3d(
                        x=[arrow_end[0], head_point[0], None],
                        y=[arrow_end[1], head_point[1], None],
                        z=[arrow_end[2], head_point[2], None],
                        mode='lines',
                        line=dict(width=1, color=arrow_color),
                        showlegend=False,
                        hoverinfo='none'
                    )
                    arrow_traces.append(head_trace)

            # Add camera position marker
            pos_trace = go.Scatter3d(
                x=[pos[0]],
                y=[pos[1]],
                z=[pos[2]],
                mode='markers+text',
                marker=dict(size=6, color=marker_color, symbol='circle'),
                text=[str(i)],
                textposition="top center",
                showlegend=False,
                hoverinfo='text'
            )
            arrow_traces.append(pos_trace)

        fig.add_traces(edge_traces)
        fig.add_traces(arrow_traces)

    # 更新布局，隐藏图例并优化性能
    fig.update_layout(
        showlegend=False,
        scene=dict(
            aspectmode='data',  # 保持数据原始比例
        ),
        uirevision='same',  # 保持视图状态，避免重新计算
    )
    
    # 使用更高效的渲染模式
    fig.update_traces(
        selector=dict(type='scatter3d'),
        hoverinfo='none'  # 禁用悬停信息以提高性能
    )
    
    # fig.show(renderer="browser")  # 使用浏览器渲染，通常更快
    fig.show()

# load mast3r model
from argparse import Namespace
torch.backends.cuda.matmul.allow_tf32 = True
torch.set_grad_enabled(False)
device = "cuda"
save_frames = False
datetime_now = str(datetime.datetime.now()).replace(" ", "_")
model = load_mast3r(device=device)

# init
args = Namespace(
    dataset="data/XJ_kf",
    config="config/base.yaml",
    save_as="default",
    no_viz=True,
    calib="",
    checkpoint="",
    checkpoint_dir="logs/checkpoints",
    checkpoint_interval=0,
)
load_config(args.config)

# 正常初始化
i, dataset, states, K, save_dir, seq_name = init(args, model, device)
logger.info(f"Initialized")
logger.info(args.dataset)
logger.info(config)

keyframes = states.keyframes
factor_graph = states.factor_graph
retrieval_database = states.retrieval_database
tracker = states.tracker

def dataset_iter(dataset):
    for i in range(len(dataset)):
        timestamp, img, img_path = dataset[i]
        # get frames last camera pose
        T_WC = (
            lietorch.Sim3.Identity(1, device=device)
            if len(keyframes) == 0
            else states.get_frame().T_WC
        )
        frame = create_frame(i, img, T_WC, img_size=dataset.img_size, img_path=img_path, device=device)
        mode = states.get_mode()
        print(mode, i, img_path)
        yield frame, mode

_iter = dataset_iter(dataset)

# 开始建图，每执行一次去一个新frame添加到图中
try:
    frame, mode = next(_iter)
except StopIteration:
    while len(states.global_optimizer_tasks) > 0:
        idx_to_process = states.global_optimizer_tasks.pop(0)
        process_keyframe_optimization(idx_to_process, keyframes, factor_graph, retrieval_database, states)
    add_new_kf = False
    states.set_mode(Mode.TERMINATED)
    print('done')

if mode == Mode.INIT:
    # Initialize via mono inference, and encoded features neeed for database
    X_init, C_init = mast3r_inference_mono(model, frame)
    frame.update_pointmap(X_init, C_init)
    add_new_kf = True
    states.set_mode(Mode.TRACKING)
    states.set_frame(frame)
    i += 1

elif mode == Mode.TRACKING:
    add_new_kf, match_info, try_reloc = tracker.track(frame)
    if add_new_kf or try_reloc:
        logger.info(f"{add_new_kf=} {try_reloc=}")
    if try_reloc:
        states.set_mode(Mode.RELOC)
    states.set_frame(frame)

if mode == Mode.RELOC:
    logger.info(f"relocating: {frame}")
    X, C = mast3r_inference_mono(model, frame)
    frame.update_pointmap(X, C)
    states.set_frame(frame)
    # 直接进行重定位
    success = relocalization(frame, keyframes, factor_graph, retrieval_database)
    if success:
        states.set_mode(Mode.TRACKING)
    else:
        logger.warning("Relocalization failed, set new trajectory")
        # 处理剩余的优化任务
        while len(states.global_optimizer_tasks) > 0:
            idx_to_process = states.global_optimizer_tasks.pop(0)
            process_keyframe_optimization(idx_to_process, keyframes, factor_graph, retrieval_database, states)
        h, w = dataset.get_img_shape()[0]
        keyframes = SimpleKeyframes(h, w, device)
        retrieval_database = load_retriever(model, device=device)
        factor_graph = FactorGraph(model, keyframes, K, device)
        tracker = FrameTracker(model, keyframes, device)
        states.new_track(keyframes, retrieval_database, factor_graph, tracker)
        states.set_mode(Mode.INIT)

if add_new_kf:
    keyframes.append(frame)
    states.queue_global_optimization(len(keyframes) - 1)
    
    # 立即处理优化任务
    if len(states.global_optimizer_tasks) > 0:
        idx_to_process = states.global_optimizer_tasks.pop(0)
        process_keyframe_optimization(idx_to_process, keyframes, factor_graph, retrieval_database, states)

# show(states.keyframes, states.factor_graph, show_cloudpoints=True, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.05, highlight_nodes=[])

states = SimpleStates()
states.load('logs', 'f20_keyframes_0', model, device)

# show(states.keyframes, states.factor_graph, show_cloudpoints=True, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.2, highlight_nodes=[])

states1 = SimpleStates()
states1.load('logs', 'XJ_kf_1', model, device)

# show(states1.keyframes, states1.factor_graph, show_cloudpoints=False, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.3, highlight_nodes=[])

states.save('logs/tmp', 'xj_kf-end')

print('before:', states1.keyframes.T_WC[1].data)
states1.factor_graph.solve_GN_rays()
print(' after:', states1.keyframes.T_WC[1].data)

states.merge(states1)

keyframes = states.keyframes
factor_graph = states.factor_graph
print(factor_graph.ii.shape, factor_graph.jj.shape, factor_graph.idx_ii2jj.shape, factor_graph.idx_jj2ii.shape, factor_graph.valid_match_j.shape, factor_graph.valid_match_i.shape, factor_graph.Q_ii2jj.shape, factor_graph.Q_jj2ii.shape, len(factor_graph.vertices), len(factor_graph.edges), len(keyframes))

keyframes = states.keyframes
factor_graph = states.factor_graph
print(factor_graph.ii.shape, factor_graph.jj.shape, factor_graph.idx_ii2jj.shape, factor_graph.idx_jj2ii.shape, factor_graph.valid_match_j.shape, factor_graph.valid_match_i.shape, factor_graph.Q_ii2jj.shape, factor_graph.Q_jj2ii.shape, len(factor_graph.vertices), len(factor_graph.edges), len(keyframes))

keyframes = states1.keyframes
factor_graph = states1.factor_graph
print(factor_graph.ii.shape, factor_graph.jj.shape, factor_graph.idx_ii2jj.shape, factor_graph.idx_jj2ii.shape, factor_graph.valid_match_j.shape, factor_graph.valid_match_i.shape, factor_graph.Q_ii2jj.shape, factor_graph.Q_jj2ii.shape, len(factor_graph.vertices), len(factor_graph.edges), len(keyframes))

merged_keyframes = SimpleKeyframes(states.keyframes.h, states.keyframes.w, device)
for frame in states.keyframes:
    merged_keyframes.append(frame)
for frame in states1.keyframes:
    merged_keyframes.append(frame)
print(len(states.keyframes), len(states1.keyframes), len(merged_keyframes))

merged_fg = FactorGraph(model, merged_keyframes, K, device)
merged_fg.ii = torch.cat([states.factor_graph.ii, states1.factor_graph.ii+len(states.keyframes)], dim=0)
merged_fg.jj = torch.cat([states.factor_graph.jj, states1.factor_graph.jj+len(states.keyframes)], dim=0)
merged_fg.idx_ii2jj = torch.cat([states.factor_graph.idx_ii2jj, states1.factor_graph.idx_ii2jj], dim=0)
merged_fg.idx_jj2ii = torch.cat([states.factor_graph.idx_jj2ii, states1.factor_graph.idx_jj2ii], dim=0)
merged_fg.valid_match_j = torch.cat([states.factor_graph.valid_match_j, states1.factor_graph.valid_match_j], dim=0)
merged_fg.valid_match_i = torch.cat([states.factor_graph.valid_match_i, states1.factor_graph.valid_match_i], dim=0)
merged_fg.Q_ii2jj = torch.cat([states.factor_graph.Q_ii2jj, states1.factor_graph.Q_ii2jj], dim=0)
merged_fg.Q_jj2ii = torch.cat([states.factor_graph.Q_jj2ii, states1.factor_graph.Q_jj2ii], dim=0)
merged_fg.vertices = states.factor_graph.vertices + (np.array(states1.factor_graph.vertices)+len(states.keyframes)).tolist()
merged_fg.edges = states.factor_graph.edges + (np.array(states1.factor_graph.edges)+len(states.keyframes)).tolist()
print(merged_fg.ii.shape, merged_fg.jj.shape, merged_fg.idx_ii2jj.shape, merged_fg.idx_jj2ii.shape, merged_fg.valid_match_j.shape, merged_fg.valid_match_i.shape, merged_fg.Q_ii2jj.shape, merged_fg.Q_jj2ii.shape, len(merged_fg.vertices), len(merged_fg.edges), len(merged_keyframes))

retriever = load_retriever(model, device=device)
for frame in states.keyframes:
    retriever.update(
        frame,
        add_after_query=True,
        k=config["retrieval"]["k"],
        min_thresh=config["retrieval"]["min_thresh"],
    )
new_edges = []
ii = []
jj = []
for j in range(len(states1.keyframes)):
    frame = states1.keyframes[j]
    retrieval_inds = retriever.update(
        frame,
        add_after_query=False,
        k=config["retrieval"]["k"],
        min_thresh=config["retrieval"]["min_thresh"],
    )
    for i in retrieval_inds:
        ii.append(i)
        jj.append(j+len(states.keyframes))
new_edges = [(i, j) for i, j in zip(ii, jj)]
print(new_edges)
print(len(new_edges))

from mast3r_slam.mast3r_utils import mast3r_match_symmetric

def mast3r_match(ii, jj, min_match_frac, is_reloc=False):
    kf_ii = [merged_fg.frames[idx] for idx in ii]
    kf_jj = [merged_fg.frames[idx] for idx in jj]
    feat_i = torch.cat([kf_i.feat for kf_i in kf_ii])
    feat_j = torch.cat([kf_j.feat for kf_j in kf_jj])
    pos_i = torch.cat([kf_i.pos for kf_i in kf_ii])
    pos_j = torch.cat([kf_j.pos for kf_j in kf_jj])
    shape_i = [kf_i.img_true_shape for kf_i in kf_ii]
    shape_j = [kf_j.img_true_shape for kf_j in kf_jj]

    (
        idx_i2j,
        idx_j2i,
        valid_match_j,
        valid_match_i,
        Qii,
        Qjj,
        Qji,
        Qij,
    ) = mast3r_match_symmetric(
        model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
    )

    batch_inds = torch.arange(idx_i2j.shape[0], device=idx_i2j.device)[
        :, None
    ].repeat(1, idx_i2j.shape[1])
    Qj = torch.sqrt(Qii[batch_inds, idx_i2j] * Qji)
    Qi = torch.sqrt(Qjj[batch_inds, idx_j2i] * Qij)

    valid_Qj = Qj > merged_fg.cfg["Q_conf"]
    valid_Qi = Qi > merged_fg.cfg["Q_conf"]
    valid_j = valid_match_j & valid_Qj
    valid_i = valid_match_i & valid_Qi
    nj = valid_j.shape[1] * valid_j.shape[2]
    ni = valid_i.shape[1] * valid_i.shape[2]
    match_frac_j = valid_j.sum(dim=(1, 2)) / nj
    match_frac_i = valid_i.sum(dim=(1, 2)) / ni

    ii_tensor = torch.as_tensor(ii, device=merged_fg.device)
    jj_tensor = torch.as_tensor(jj, device=merged_fg.device)

    invalid_edges = torch.minimum(match_frac_j, match_frac_i) < min_match_frac
    consecutive_edges = ii_tensor == (jj_tensor - 1)
    invalid_edges = (~consecutive_edges) & invalid_edges

    valid_edges = ~invalid_edges
    ii_tensor = ii_tensor[valid_edges]
    jj_tensor = jj_tensor[valid_edges]
    idx_i2j = idx_i2j[valid_edges]
    idx_j2i = idx_j2i[valid_edges]
    valid_match_j = valid_match_j[valid_edges]
    valid_match_i = valid_match_i[valid_edges]
    Qj = Qj[valid_edges]
    Qi = Qi[valid_edges]

    return valid_edges, ii_tensor, jj_tensor, idx_i2j, idx_j2i, valid_match_j, valid_match_i, Qj, Qi

merged_valid_edges = []
merged_ii_tensor = torch.as_tensor([], dtype=torch.long, device=device)
merged_jj_tensor = torch.as_tensor([], dtype=torch.long, device=device)
merged_idx_i2j = torch.as_tensor([], dtype=torch.long, device=device)
merged_idx_j2i = torch.as_tensor([], dtype=torch.long, device=device)
merged_valid_match_j = torch.as_tensor([], dtype=torch.bool, device=device)
merged_valid_match_i = torch.as_tensor([], dtype=torch.bool, device=device)
merged_Qj = torch.as_tensor([], dtype=torch.float32, device=device)
merged_Qi = torch.as_tensor([], dtype=torch.float32, device=device)

batch_size = 10
for i in range(0, len(new_edges), batch_size):
    ii_i = [e[0] for e in new_edges[i:i+batch_size]]
    jj_i = [e[1] for e in new_edges[i:i+batch_size]]
    valid_edges, ii_tensor, jj_tensor, idx_i2j, idx_j2i, valid_match_j, valid_match_i, Qj, Qi = mast3r_match(ii_i, jj_i, min_match_frac=0.3, is_reloc=False)
    merged_valid_edges.extend(valid_edges)
    merged_ii_tensor = torch.cat([merged_ii_tensor, ii_tensor], dim=0)
    merged_jj_tensor = torch.cat([merged_jj_tensor, jj_tensor], dim=0)
    merged_idx_i2j = torch.cat([merged_idx_i2j, idx_i2j], dim=0)
    merged_idx_j2i = torch.cat([merged_idx_j2i, idx_j2i], dim=0)
    merged_valid_match_j = torch.cat([merged_valid_match_j, valid_match_j], dim=0)
    merged_valid_match_i = torch.cat([merged_valid_match_i, valid_match_i], dim=0)
    merged_Qj = torch.cat([merged_Qj, Qj], dim=0)
    merged_Qi = torch.cat([merged_Qi, Qi], dim=0)

print(sum(merged_valid_edges), merged_ii_tensor.shape, merged_jj_tensor.shape, merged_idx_i2j.shape, merged_idx_j2i.shape, merged_valid_match_j.shape, merged_valid_match_i.shape, merged_Qj.shape, merged_Qi.shape)

valid_edges = [e for e, v in zip(new_edges, merged_valid_edges) if v]
print(new_edges)
print(valid_edges)

print(len(valid_edges))
print(merged_ii_tensor.shape)
print(merged_jj_tensor.shape)
print(merged_idx_i2j.shape)
print(merged_idx_j2i.shape)
print(merged_valid_match_j.shape)
print(merged_valid_match_i.shape)
print(merged_Qj.shape)
print(merged_Qi.shape)

print(len(merged_fg.vertices))
print(len(merged_fg.edges))
print(merged_fg.ii.shape)
print(merged_fg.jj.shape)
print(merged_fg.idx_ii2jj.shape)
print(merged_fg.idx_jj2ii.shape)
print(merged_fg.valid_match_j.shape)
print(merged_fg.valid_match_i.shape)
print(merged_fg.Q_ii2jj.shape)
print(merged_fg.Q_jj2ii.shape)

merged_fg.edges.extend(valid_edges)
merged_fg.ii = torch.cat([merged_fg.ii, merged_ii_tensor], dim=0)
merged_fg.jj = torch.cat([merged_fg.jj, merged_jj_tensor], dim=0)
merged_fg.idx_ii2jj = torch.cat([merged_fg.idx_ii2jj, merged_idx_i2j], dim=0)
merged_fg.idx_jj2ii = torch.cat([merged_fg.idx_jj2ii, merged_idx_j2i], dim=0)
merged_fg.valid_match_j = torch.cat([merged_fg.valid_match_j, merged_valid_match_j], dim=0)
merged_fg.valid_match_i = torch.cat([merged_fg.valid_match_i, merged_valid_match_i], dim=0)
merged_fg.Q_ii2jj = torch.cat([merged_fg.Q_ii2jj, merged_Qj], dim=0)
merged_fg.Q_jj2ii = torch.cat([merged_fg.Q_jj2ii, merged_Qi], dim=0)
print(len(merged_fg.vertices))
print(len(merged_fg.edges))
print(merged_fg.ii.shape)
print(merged_fg.jj.shape)
print(merged_fg.idx_ii2jj.shape)
print(merged_fg.idx_jj2ii.shape)
print(merged_fg.valid_match_j.shape)
print(merged_fg.valid_match_i.shape)
print(merged_fg.Q_ii2jj.shape)
print(merged_fg.Q_jj2ii.shape)

# show(merged_fg.frames, merged_fg, show_cloudpoints=False, show_colors=True, show_poses=True, show_arrows=True, voxel_size=0.3, highlight_nodes=[])

print('before:', merged_fg.frames.T_WC[491].data)
merged_fg.solve_GN_rays()
print(' after:', merged_fg.frames.T_WC[491].data)

# show(merged_fg.frames, merged_fg, show_cloudpoints=False, show_colors=True, show_poses=True, show_arrows=False, voxel_size=0.3, highlight_nodes=[])